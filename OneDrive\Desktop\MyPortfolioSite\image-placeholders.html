<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Placeholder Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e3a8a;
            text-align: center;
            margin-bottom: 30px;
        }
        .image-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
        }
        .image-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .image-placeholder {
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            font-weight: bold;
        }
        .profile-placeholder {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .project-placeholder {
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .client-placeholder {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            color: #1565c0;
            margin-top: 0;
        }
        .download-links {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }
        .download-links a {
            color: #1e3a8a;
            text-decoration: none;
            margin-right: 15px;
            font-weight: 500;
        }
        .download-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Image Placeholder Guide</h1>
        
        <div class="instructions">
            <h3>📋 Instructions</h3>
            <p>Your portfolio website is ready! To complete the setup, you need to add the following images to the <code>assets/images/</code> folder:</p>
            <ul>
                <li><strong>Replace placeholder images</strong> with your actual photos</li>
                <li><strong>Use recommended dimensions</strong> for best results</li>
                <li><strong>Optimize images</strong> for web (compress to reduce file size)</li>
                <li><strong>Use common formats</strong> like JPG, PNG, or WebP</li>
            </ul>
        </div>

        <div class="image-section">
            <h3>👤 Profile Photo</h3>
            <p><strong>File:</strong> profile.jpg | <strong>Size:</strong> 150x150px (square) | <strong>Format:</strong> JPG/PNG</p>
            <div class="image-placeholder profile-placeholder">
                Your Photo Here
            </div>
            <div class="download-links">
                <strong>Free Stock Photos:</strong>
                <a href="https://unsplash.com/s/photos/professional-headshot" target="_blank">Unsplash</a>
                <a href="https://www.pexels.com/search/professional%20portrait/" target="_blank">Pexels</a>
                <a href="https://pixabay.com/photos/search/professional%20portrait/" target="_blank">Pixabay</a>
            </div>
        </div>

        <div class="image-section">
            <h3>💼 Project Screenshots</h3>
            <p><strong>Files:</strong> project1.jpg to project6.jpg | <strong>Size:</strong> 400x200px | <strong>Format:</strong> JPG/PNG</p>
            <div class="image-grid">
                <div class="image-placeholder project-placeholder">E-Commerce Platform</div>
                <div class="image-placeholder project-placeholder">Agency Website</div>
                <div class="image-placeholder project-placeholder">SaaS Landing</div>
                <div class="image-placeholder project-placeholder">Restaurant Site</div>
                <div class="image-placeholder project-placeholder">Real Estate</div>
                <div class="image-placeholder project-placeholder">Fitness App</div>
            </div>
            <div class="download-links">
                <strong>Website Screenshots:</strong>
                <a href="https://www.screely.com/" target="_blank">Screely</a>
                <a href="https://browserframe.com/" target="_blank">Browser Frame</a>
                <a href="https://mockuphone.com/" target="_blank">MockuPhone</a>
            </div>
        </div>

        <div class="image-section">
            <h3>👥 Client Photos</h3>
            <p><strong>Files:</strong> client1.jpg to client3.jpg | <strong>Size:</strong> 60x60px (square) | <strong>Format:</strong> JPG/PNG</p>
            <div class="image-grid">
                <div class="image-placeholder client-placeholder">Client 1</div>
                <div class="image-placeholder client-placeholder">Client 2</div>
                <div class="image-placeholder client-placeholder">Client 3</div>
            </div>
            <div class="download-links">
                <strong>Professional Photos:</strong>
                <a href="https://thispersondoesnotexist.com/" target="_blank">AI Generated</a>
                <a href="https://unsplash.com/s/photos/business-person" target="_blank">Business Photos</a>
                <a href="https://www.pexels.com/search/professional/" target="_blank">Professional</a>
            </div>
        </div>

        <div class="instructions">
            <h3>🚀 Next Steps</h3>
            <ol>
                <li><strong>Add your images</strong> to the <code>assets/images/</code> folder</li>
                <li><strong>Update your CV</strong> in <code>assets/documents/Mohamed_Abdulkadir_CV.pdf</code></li>
                <li><strong>Customize content</strong> in <code>index.html</code> and <code>script.js</code></li>
                <li><strong>Test your website</strong> by opening <code>index.html</code> in a browser</li>
                <li><strong>Deploy online</strong> using Netlify, Vercel, or GitHub Pages</li>
            </ol>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #1e3a8a, #3b82f6); color: white; border-radius: 8px;">
            <h3>🎉 Your Portfolio is Ready!</h3>
            <p>Once you add your images, your professional portfolio will be complete and ready to impress clients and employers.</p>
        </div>
    </div>
</body>
</html>
