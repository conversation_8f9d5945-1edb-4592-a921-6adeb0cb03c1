// ===== GLOBAL VARIABLES =====
let currentTestimonial = 0;
let portfolioData = [];
let testimonialsData = [];

// ===== DOM CONTENT LOADED =====
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS (Animate On Scroll)
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });

    // Initialize all components
    initNavigation();
    initHeroAnimations();
    initSkillBars();
    initPortfolio();
    initTestimonials();
    initContactForm();
    initScrollEffects();
});

// ===== NAVIGATION =====
function initNavigation() {
    const navbar = document.getElementById('navbar');
    const mobileMenu = document.getElementById('mobile-menu');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    mobileMenu.addEventListener('click', function(e) {
        e.stopPropagation();
        mobileMenu.classList.toggle('active');
        navMenu.classList.toggle('active');
        document.body.style.overflow = navMenu.classList.contains('active') ? 'hidden' : '';
    });

    // Close mobile menu when clicking on a link
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            mobileMenu.classList.remove('active');
            navMenu.classList.remove('active');
            document.body.style.overflow = '';
        });
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!navbar.contains(e.target) && navMenu.classList.contains('active')) {
            mobileMenu.classList.remove('active');
            navMenu.classList.remove('active');
            document.body.style.overflow = '';
        }
    });

    // Close mobile menu on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && navMenu.classList.contains('active')) {
            mobileMenu.classList.remove('active');
            navMenu.classList.remove('active');
            document.body.style.overflow = '';
        }
    });

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Active navigation link
    window.addEventListener('scroll', debounce(function() {
        let current = '';
        const sections = document.querySelectorAll('section');

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            if (scrollY >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    }, 100));
}

// ===== HERO ANIMATIONS =====
function initHeroAnimations() {
    // Create floating particles
    createParticles();

    // Smooth scroll for hero buttons
    const heroButtons = document.querySelectorAll('.hero-buttons .btn');
    heroButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

function createParticles() {
    const particlesContainer = document.getElementById('particles');
    const particleCount = 50;

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.cssText = `
            position: absolute;
            width: ${Math.random() * 4 + 2}px;
            height: ${Math.random() * 4 + 2}px;
            background: ${Math.random() > 0.5 ? '#1e3a8a' : '#f59e0b'};
            border-radius: 50%;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            opacity: ${Math.random() * 0.5 + 0.2};
            animation: float ${Math.random() * 10 + 10}s infinite linear;
        `;
        particlesContainer.appendChild(particle);
    }
}

// ===== SKILL BARS ANIMATION =====
function initSkillBars() {
    const skillBars = document.querySelectorAll('.skill-progress');
    
    const animateSkillBars = () => {
        skillBars.forEach(bar => {
            const rect = bar.getBoundingClientRect();
            if (rect.top < window.innerHeight && rect.bottom > 0) {
                const width = bar.getAttribute('data-width');
                bar.style.width = width;
            }
        });
    };

    // Animate on scroll
    window.addEventListener('scroll', animateSkillBars);
    // Animate on load if already in view
    setTimeout(animateSkillBars, 500);
}

// ===== PORTFOLIO =====
function initPortfolio() {
    // Portfolio data
    portfolioData = [
        {
            id: 1,
            title: "E-Commerce Platform",
            description: "Modern e-commerce website with shopping cart, payment integration, and admin dashboard.",
            image: "assets/images/project1.jpg",
            category: "ecommerce",
            tags: ["React", "Node.js", "MongoDB"],
            liveUrl: "https://example.com",
            codeUrl: "https://github.com/example"
        },
        {
            id: 2,
            title: "Digital Agency Website",
            description: "Creative agency portfolio with stunning animations and responsive design.",
            image: "assets/images/project2.jpg",
            category: "agency",
            tags: ["HTML", "CSS", "JavaScript"],
            liveUrl: "https://example.com",
            codeUrl: "https://github.com/example"
        },
        {
            id: 3,
            title: "SaaS Landing Page",
            description: "High-converting landing page for a SaaS product with lead generation forms.",
            image: "assets/images/project3.jpg",
            category: "landing",
            tags: ["WordPress", "PHP", "MySQL"],
            liveUrl: "https://example.com",
            codeUrl: "https://github.com/example"
        },
        {
            id: 4,
            title: "Restaurant Website",
            description: "Beautiful restaurant website with online menu and reservation system.",
            image: "assets/images/project4.jpg",
            category: "agency",
            tags: ["Vue.js", "Firebase", "CSS3"],
            liveUrl: "https://example.com",
            codeUrl: "https://github.com/example"
        },
        {
            id: 5,
            title: "Real Estate Platform",
            description: "Property listing website with advanced search and filtering capabilities.",
            image: "assets/images/project5.jpg",
            category: "ecommerce",
            tags: ["React", "Express", "PostgreSQL"],
            liveUrl: "https://example.com",
            codeUrl: "https://github.com/example"
        },
        {
            id: 6,
            title: "Fitness App Landing",
            description: "Mobile app landing page with app store integration and user testimonials.",
            image: "assets/images/project1.jpg",
            category: "landing",
            tags: ["Bootstrap", "jQuery", "SCSS"],
            liveUrl: "https://example.com",
            codeUrl: "https://github.com/example"
        }
    ];

    renderPortfolio();
    initPortfolioFilter();
}

function renderPortfolio(filter = 'all') {
    const portfolioGrid = document.querySelector('.portfolio-grid');
    const filteredProjects = filter === 'all' 
        ? portfolioData 
        : portfolioData.filter(project => project.category === filter);

    portfolioGrid.innerHTML = filteredProjects.map(project => `
        <div class="project-card" data-category="${project.category}">
            <div class="project-image">
                <img src="${project.image}" alt="${project.title}">
                <div class="project-overlay">
                    <a href="${project.liveUrl}" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                    <a href="${project.codeUrl}" target="_blank" class="btn btn-secondary">
                        <i class="fab fa-github"></i>
                    </a>
                </div>
            </div>
            <div class="project-content">
                <h3 class="project-title">${project.title}</h3>
                <p class="project-description">${project.description}</p>
                <div class="project-tags">
                    ${project.tags.map(tag => `<span class="project-tag">${tag}</span>`).join('')}
                </div>
                <div class="project-links">
                    <a href="${project.liveUrl}" target="_blank" class="project-link">Live Demo</a>
                    <a href="${project.codeUrl}" target="_blank" class="project-link">View Code</a>
                </div>
            </div>
        </div>
    `).join('');
}

function initPortfolioFilter() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            
            // Get filter value and render portfolio
            const filter = this.getAttribute('data-filter');
            renderPortfolio(filter);
        });
    });
}

// ===== TESTIMONIALS =====
function initTestimonials() {
    testimonialsData = [
        {
            id: 1,
            name: "Sarah Johnson",
            role: "Marketing Director",
            company: "Ibgaro Realtors",
            image: "assets/images/client1.jpg",
            text: "Mohamed redesigned our real estate website and it doubled our client inquiries. Super professional and fast! His attention to detail and understanding of our business needs was exceptional."
        },
        {
            id: 2,
            name: "David Chen",
            role: "CEO",
            company: "TechStart Solutions",
            image: "assets/images/client2.jpg",
            text: "Working with Mohamed was a game-changer for our startup. He delivered a stunning website that perfectly represents our brand and converts visitors into customers."
        },
        {
            id: 3,
            name: "Maria Rodriguez",
            role: "Restaurant Owner",
            company: "Bella Vista Restaurant",
            image: "assets/images/client3.jpg",
            text: "Our new website has increased online reservations by 300%! Mohamed understood our vision and created something beyond our expectations. Highly recommended!"
        }
    ];

    renderTestimonials();
    initTestimonialNavigation();
}

function renderTestimonials() {
    const testimonialTrack = document.getElementById('testimonial-track');
    const testimonialDots = document.getElementById('testimonial-dots');

    // Render testimonial cards
    testimonialTrack.innerHTML = testimonialsData.map(testimonial => `
        <div class="testimonial-card">
            <div class="testimonial-content">
                <p class="testimonial-text">${testimonial.text}</p>
            </div>
            <div class="testimonial-author">
                <img src="${testimonial.image}" alt="${testimonial.name}" class="author-image">
                <div class="author-info">
                    <h4>${testimonial.name}</h4>
                    <p>${testimonial.role} at ${testimonial.company}</p>
                </div>
            </div>
        </div>
    `).join('');

    // Render dots
    testimonialDots.innerHTML = testimonialsData.map((_, index) => `
        <span class="dot ${index === 0 ? 'active' : ''}" data-slide="${index}"></span>
    `).join('');
}

function initTestimonialNavigation() {
    const prevBtn = document.getElementById('prev-testimonial');
    const nextBtn = document.getElementById('next-testimonial');
    const dots = document.querySelectorAll('.dot');

    // Previous button
    prevBtn.addEventListener('click', () => {
        currentTestimonial = currentTestimonial > 0 ? currentTestimonial - 1 : testimonialsData.length - 1;
        updateTestimonialSlide();
    });

    // Next button
    nextBtn.addEventListener('click', () => {
        currentTestimonial = currentTestimonial < testimonialsData.length - 1 ? currentTestimonial + 1 : 0;
        updateTestimonialSlide();
    });

    // Dots navigation
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            currentTestimonial = index;
            updateTestimonialSlide();
        });
    });

    // Auto-play testimonials
    setInterval(() => {
        currentTestimonial = currentTestimonial < testimonialsData.length - 1 ? currentTestimonial + 1 : 0;
        updateTestimonialSlide();
    }, 5000);
}

function updateTestimonialSlide() {
    const track = document.getElementById('testimonial-track');
    const dots = document.querySelectorAll('.dot');
    
    track.style.transform = `translateX(-${currentTestimonial * 100}%)`;
    
    dots.forEach((dot, index) => {
        dot.classList.toggle('active', index === currentTestimonial);
    });
}

// ===== CONTACT FORM =====
function initContactForm() {
    const contactForm = document.getElementById('contact-form');
    
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);
        
        // Simple validation
        if (!data.name || !data.email || !data.message) {
            showNotification('Please fill in all required fields.', 'error');
            return;
        }
        
        // Simulate form submission
        showNotification('Thank you! Your message has been sent successfully.', 'success');
        this.reset();
    });
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background: ${type === 'success' ? '#10b981' : '#ef4444'};
        color: white;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 5000);
}

// ===== SCROLL EFFECTS =====
function initScrollEffects() {
    // Smooth scroll for all anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// ===== UTILITY FUNCTIONS =====
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
