/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #1e3a8a;
    --secondary-color: #3b82f6;
    --accent-color: #f59e0b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --white: #ffffff;
    --light-bg: #f8fafc;
    --dark-bg: #0f172a;
    --border-color: #e5e7eb;
    
    /* Typography */
    --font-family: 'Inter', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

html {
    scroll-behavior: smooth;
    overflow-x: hidden;
    max-width: 100vw;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--white);
    overflow-x: hidden;
    max-width: 100vw;
    box-sizing: border-box;
}

*, *::before, *::after {
    box-sizing: border-box;
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-sm);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.accent {
    color: var(--accent-color);
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 12px 24px;
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    max-width: 100vw;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all var(--transition-normal);
    overflow-x: hidden;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
    width: 100%;
    box-sizing: border-box;
}

.nav-logo a {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: width var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    margin: 3px 0;
    transition: all var(--transition-normal);
    border-radius: 2px;
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%231e3a8a" opacity="0.3"><animate attributeName="opacity" values="0.3;0.8;0.3" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="30" r="1.5" fill="%23f59e0b" opacity="0.4"><animate attributeName="opacity" values="0.4;0.9;0.4" dur="2s" repeatCount="indefinite"/></circle><circle cx="60" cy="70" r="2.5" fill="%233b82f6" opacity="0.2"><animate attributeName="opacity" values="0.2;0.7;0.2" dur="4s" repeatCount="indefinite"/></circle></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-100px) rotate(360deg); }
}

.hero-container {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    padding: 0 var(--spacing-sm);
    margin-top: 15%;
}

.hero-image {
    margin-bottom: var(--spacing-lg);
}

.profile-img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid var(--white);
    box-shadow: var(--shadow-xl);
    transition: transform var(--transition-normal);
}

.profile-img:hover {
    transform: scale(1.05);
}

/* Author image styling */
.author-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--accent-color);
    flex-shrink: 0;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    line-height: 1.2;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-bottom: var(--spacing-2xl);
    flex-wrap: wrap;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    flex-wrap: wrap;
    padding-bottom: 1.5rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border: 2px solid var(--primary-color);
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) rotate(-45deg);
    }
    40% {
        transform: translateY(-10px) rotate(-45deg);
    }
    60% {
        transform: translateY(-5px) rotate(-45deg);
    }
}

/* ===== ABOUT SECTION ===== */
.about {
    padding: var(--spacing-2xl) 0;
    background: var(--white);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: start;
}

.about-text h3 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.about-text p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.7;
}

.about-highlights {
    margin: var(--spacing-xl) 0;
}

.highlight {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.highlight i {
    color: var(--accent-color);
    width: 20px;
}

.skills h3 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.skills-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.skill-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.skill-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    color: var(--white);
    font-size: var(--font-size-xl);
}

.skill-info {
    flex: 1;
}

.skill-name {
    display: block;
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.skill-bar {
    width: 100%;
    height: 8px;
    background: var(--light-bg);
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 4px;
    width: 0;
    transition: width 1s ease-in-out;
}

/* ===== PORTFOLIO SECTION ===== */
.portfolio {
    padding: var(--spacing-2xl) 0;
    background: var(--light-bg);
}

.portfolio-filter {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-2xl);
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 20px;
    background: var(--white);
    border: 2px solid var(--border-color);
    border-radius: 25px;
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    display: inline-block;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

/* CSS-only filtering - Show all by default */
.project-card {
    display: block;
}

/* Hide all when a specific filter is targeted */
#filter-ecommerce:target ~ .portfolio-grid .project-card:not([data-category="ecommerce"]) {
    display: none;
}

#filter-agency:target ~ .portfolio-grid .project-card:not([data-category="agency"]) {
    display: none;
}

#filter-landing:target ~ .portfolio-grid .project-card:not([data-category="landing"]) {
    display: none;
}

/* Active filter button styling */
#filter-all:target,
#filter-ecommerce:target,
#filter-agency:target,
#filter-landing:target {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.project-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    opacity: 1;
    transform: scale(1);
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.project-card.hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.project-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(30, 58, 138, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-content {
    padding: var(--spacing-lg);
}

.project-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.project-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.project-tags {
    display: flex;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.project-tag {
    padding: 4px 12px;
    background: var(--light-bg);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    border-radius: 15px;
}

.project-links {
    display: flex;
    gap: var(--spacing-sm);
}

.project-link {
    padding: 8px 16px;
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    text-decoration: none;
    border-radius: 6px;
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: all var(--transition-normal);
}

.project-link:hover {
    background: var(--primary-color);
    color: var(--white);
}

.portfolio-cta {
    text-align: center;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--white);
        width: 100vw;
        max-width: 100vw;
        text-align: center;
        transition: left 0.3s ease-in-out;
        box-shadow: var(--shadow-lg);
        padding: var(--spacing-lg) 0;
        z-index: 999;
        height: calc(100vh - 70px);
        overflow-y: auto;
        overflow-x: hidden;
        box-sizing: border-box;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-item {
        margin: var(--spacing-sm) 0;
    }

    .nav-link {
        display: block;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-lg);
        border-radius: 8px;
        margin: 0 var(--spacing-lg);
        transition: all var(--transition-normal);
    }

    .nav-link:hover {
        background-color: var(--light-bg);
        color: var(--primary-color);
    }

    .nav-toggle {
        display: flex;
        z-index: 1001;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    /* Overlay for mobile menu */
    .nav-menu.active::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: -1;
    }

    .hero-title {
        font-size: var(--font-size-4xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        gap: var(--spacing-md);
        padding-bottom: 1.5rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
    }

    .section-title {
        font-size: var(--font-size-3xl);
    }
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials {
    padding: var(--spacing-2xl) 0;
    background: var(--white);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    max-width: 1200px;
    margin: 0 auto;
}

.testimonial-card {
    padding: var(--spacing-xl);
    text-align: center;
    background: var(--light-bg);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.testimonial-content {
    background: var(--white);
    padding: var(--spacing-lg);
    border-radius: 12px;
    margin-bottom: var(--spacing-lg);
    position: relative;
    border: 2px solid var(--border-color);
}

.testimonial-content::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: var(--accent-color);
    font-family: serif;
}

.testimonial-text {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: var(--spacing-md);
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.author-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--accent-color);
}

.author-info h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.author-info p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}



/* ===== CONTACT SECTION ===== */
.contact {
    padding: var(--spacing-2xl) 0;
    background: var(--light-bg);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: start;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-sm);
}

.contact-info h3 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.contact-info p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.7;
}

.contact-details {
    margin-bottom: var(--spacing-xl);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.contact-item i {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    border-radius: 12px;
    font-size: var(--font-size-lg);
}

.contact-item .label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.contact-item a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.contact-item a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: var(--spacing-sm);
}

.social-link {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--white);
    color: var(--primary-color);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    text-decoration: none;
    font-size: var(--font-size-lg);
    transition: all var(--transition-normal);
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* ===== CONTACT FORM ===== */
.contact-form {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    transition: border-color var(--transition-normal);
    background: var(--white);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* ===== FOOTER ===== */
.footer {
    background: var(--dark-bg);
    color: var(--white);
    padding: var(--spacing-xl) 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-text p {
    margin-bottom: var(--spacing-xs);
    color: #94a3b8;
}

.coffee-link {
    color: var(--accent-color);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.coffee-link:hover {
    color: #fbbf24;
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-links a {
    color: #94a3b8;
    text-decoration: none;
    transition: color var(--transition-normal);
}

.footer-links a:hover {
    color: var(--white);
}

/* Tablet styles */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-md);
    }
    .hero-container{
        margin-top: 25%;
    }
    .hero-stats{
        padding-bottom: 1.5rem;
    }

    .about-content {
        gap: var(--spacing-xl);
    }

    .portfolio-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .contact-content {
        gap: var(--spacing-xl);
        padding: 0 var(--spacing-md);
    }
}

/* Medium tablet styles */
@media (max-width: 768px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .contact-info {
        text-align: center;
    }

    .contact-details {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .contact-item {
        justify-content: center;
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }
}

/* Mobile styles */
@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    .hero-container{
        margin-top: 25%;
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .profile-img {
        width: 120px;
        height: 120px;
    }

    .portfolio-filter {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .testimonial-author {
        flex-direction: column;
        text-align: center;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }

    .contact-form {
        padding: var(--spacing-lg);
        margin: 0 var(--spacing-sm);
    }

    .contact-content {
        padding: 0 var(--spacing-sm);
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .contact-item i {
        margin-bottom: var(--spacing-xs);
    }
}
